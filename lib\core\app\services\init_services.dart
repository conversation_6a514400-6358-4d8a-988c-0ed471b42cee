import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:onechurch/core/app/constants/enums.dart';
import 'package:onechurch/core/app/services/http_service.dart';
import 'package:onechurch/core/app/services/storage_service.dart';
import 'package:onechurch/features/events/controllers/event_controller.dart'
    show EventController;
import 'package:onechurch/features/finances/controllers/finance_controller.dart';
import 'package:onechurch/features/finances/controllers/transactions_controller.dart';
// Finance controllers removed - initialized via FinanceBinding in routes.dart
import 'package:onechurch/features/group/controllers/group_controller.dart'
    show GroupController;
import 'package:onechurch/features/hymns/controllers/hymn_controller.dart';
import 'package:onechurch/features/members/services/member_service.dart';
import 'package:onechurch/features/notifications/controllers/notification_controller.dart';
// Settings controller removed - initialized via SettingsBinding in routes.dart
import 'package:onechurch/features/sms/controllers/sms_controller.dart';
import 'package:onechurch/features/sms/controllers/sms_requests_controller.dart';
// Staff controller removed - initialized via StaffBinding in routes.dart
import 'package:onechurch/features/staff_roles/controllers/staff_role_assignment_controller.dart';
import 'package:onechurch/features/staff_roles/controllers/staff_roles_controller.dart';
import '../../../features/members/controllers/member_controller.dart';
import '../../../features/auth/controllers/auth_controller.dart';
// Inventory controllers removed - initialized via InventoryBinding in routes.dart
import 'auth_service.dart';
import 'location_service.dart';
// import '../../../features/group/controllers/group_controller.dart';
// import '../../../features/events/controllers/event_controller.dart';
import '../../../features/sermons/controllers/sermon_controller.dart';
import '../../../features/announcements/controllers/announcement_controller.dart';
// import '../../../features/profile/controllers/profile_controller.dart';
// import '../../../features/finances/controllers/finance_controller.dart';
// import '../../../features/settings/controllers/settings_controller.dart';
// import '../../../features/communications/controllers/comms_controller.dart';

class InitServices {
  static Future<void> init() async {
    // Configure URL strategy for web - must be called early
    if (kIsWeb) {
      try {
        // setPathUrlStrategy();
      } catch (e) {
        debugPrint('Error setting URL strategy: $e');
        // Continue execution even if setting URL strategy fails
      }
    }

    // First initialize all services
    await _initServices();
    Get.put(HttpService());
    final HttpService httpService = Get.find();
    httpService.initializeDio();
    final enumService = Get.put(EnumDataService());
    await enumService.fetchEnumData();

    // Then initialize all controllers
    _initControllers();
  }

  // Initialize services
  static Future<void> _initServices() async {
    // Initialize StorageService first (required by other services)
    Get.put<StorageService>(StorageService());

    // Initialize and register AuthService
    final authService = Get.put<AuthService>(AuthService());
    authService.init();

    // Initialize and register LocationService
    Get.put<LocationService>(LocationService());
    // LocationService will auto-initialize via onInit()
  }

  // Initialize controllers with lazyPut
  static void _initControllers() {
    // Auth features
    Get.lazyPut<AuthController>(() => AuthController(), fenix: true);
    Get.lazyPut(() => MemberService(), fenix: true);
    Get.lazyPut<HymnController>(() => HymnController(), fenix: true);
     Get.lazyPut<TransactionsController>(() => TransactionsController(), fenix: true);
    Get.lazyPut<NotificationController>(
      () => NotificationController(),
      fenix: true,
    );
    Get.lazyPut<StaffRolesController>(
      () => StaffRolesController(),
      fenix: true,
    );
     Get.lazyPut<FinanceController>(
      () => FinanceController(),
      fenix: true,
    );
    Get.lazyPut<StaffRoleAssignmentController>(
      () => StaffRoleAssignmentController(),
      fenix: true,
    );
    // StaffController removed - initialized via StaffBinding in routes.dart
    // Sermons features

    Get.lazyPut<SermonController>(() => SermonController(), fenix: true);
    // Events features
    Get.lazyPut<EventController>(() => EventController(), fenix: true);
    // Group features
    Get.lazyPut<GroupController>(() => GroupController(), fenix: true);

    // SettingsController removed - initialized via SettingsBinding in routes.dart
    // FinanceController removed - initialized via FinanceBinding in routes.dart
    // TransactionsController removed - initialized via FinanceBinding in routes.dart

    Get.lazyPut(() => SmsController(), fenix: true);
    Get.lazyPut(() => SmsRequestsController(), fenix: true);
    Get.lazyPut(() => MemberController(), fenix: true);
    Get.lazyPut(() => AnnouncementController(), fenix: true);

    // Inventory features removed - initialized via InventoryBinding in routes.dart
  }
}
