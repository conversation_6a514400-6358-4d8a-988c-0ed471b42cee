import 'package:flutter/material.dart';
import 'package:flutter_iconly/flutter_iconly.dart';
import 'package:go_router/go_router.dart';
import 'package:onechurch/core/app/widgets/custom_button.dart';
import '../../models/media_model.dart';

class MediaConfirmationScreen extends StatelessWidget {
  final List<MediaModel> selectedMedia;
  final Function(List<MediaModel>) onConfirm;

  const MediaConfirmationScreen({
    super.key,
    required this.selectedMedia,
    required this.onConfirm,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Confirm Selection')),
      body: Column(
        children: [
          Expanded(
            child:
                selectedMedia.isEmpty
                    ? const Center(child: Text('No media selected'))
                    : ListView.builder(
                      itemCount: selectedMedia.length,
                      itemBuilder: (context, index) {
                        final media = selectedMedia[index];
                        return Card(
                          margin: const EdgeInsets.symmetric(
                            horizontal: 16,
                            vertical: 8,
                          ),
                          elevation: 2,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(12.0),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                // Media preview with larger size
                                Container(
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(
                                      color: Colors.grey.shade300,
                                    ),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(7),
                                    child:
                                        media.mediaUrl != null
                                            ? Image.network(
                                              // Fix URL formatting issue by removing any trailing comma
                                              media.mediaUrl!.replaceAll(
                                                ',',
                                                '',
                                              ),
                                              fit: BoxFit.cover,
                                              errorBuilder:
                                                  (
                                                    context,
                                                    error,
                                                    stackTrace,
                                                  ) => const Center(
                                                    child: Icon(
                                                      Icons.broken_image,
                                                      size: 40,
                                                      color: Colors.grey,
                                                    ),
                                                  ),
                                            )
                                            : const Center(
                                              child: Icon(
                                                Icons.image_not_supported,
                                                size: 40,
                                                color: Colors.grey,
                                              ),
                                            ),
                                  ),
                                ),
                                const SizedBox(width: 16),
                                // Media information
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        media.title ?? 'Untitled',
                                        style: const TextStyle(
                                          fontWeight: FontWeight.bold,
                                          fontSize: 16,
                                        ),
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                      const SizedBox(height: 4),
                                      Text(
                                        'Type: ${media.type ?? 'Unknown'} • Size: ${_formatSize(media.sizeBytes)}',
                                        style: TextStyle(
                                          color: Colors.grey.shade700,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                // Remove button
                                FilledButton.icon(
                                  icon: const Icon(
                                    IconlyBold.delete,
                                    color: Colors.white,
                                  ),
                                  label: const Text(
                                    'Remove',
                                    style: TextStyle(color: Colors.white),
                                  ),

                                  onPressed: () {
                                    _removeMedia(context, index);
                                  },
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
          ),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.1),
                  blurRadius: 4,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    '${selectedMedia.length} item${selectedMedia.length != 1 ? 's' : ''} selected',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
                Row(
                  children: [
                    FilledButton.icon(
                      onPressed: () => context.pop(),

                      icon: Icon(IconlyLight.closeSquare),
                      label: const Text('Cancel'),
                    ),
                    const SizedBox(width: 16),
                    FilledButton.icon(
                      onPressed: () {
                        onConfirm(selectedMedia);
                        // Navigate back after confirming
                        context.pop();
                      },

                      icon: const Icon(IconlyBold.tickSquare),
                      label: const Text(
                        'Confirm',
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _removeMedia(BuildContext context, int index) {
    // Show confirmation dialog before removing
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Remove Media'),
            content: const Text(
              'Are you sure you want to remove this media item?',
            ),
            actions: [
              FilledButton.icon(
                onPressed: () => context.pop(),

                icon: Icon(IconlyLight.closeSquare),
                label: const Text('Cancel'),
              ),
              FilledButton.icon(
                onPressed: () {
                  context.pop(); // Close dialog

                  final updatedList = List<MediaModel>.from(selectedMedia);
                  updatedList.removeAt(index);

                  // Replace current screen with updated list using go_router
                  context.pushReplacement(
                    '/media/confirmation',
                    extra: {
                      'selectedMedia': updatedList,
                      'onConfirm': onConfirm,
                    },
                  );
                },

                label: const Text('Remove'),
              ),
            ],
          ),
    );
  }

  String _formatSize(int? sizeBytes) {
    if (sizeBytes == null) return 'Unknown';

    const units = ['B', 'KB', 'MB', 'GB'];
    double size = sizeBytes.toDouble();
    int unitIndex = 0;

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024;
      unitIndex++;
    }

    return '${size.toStringAsFixed(1)} ${units[unitIndex]}';
  }
}
