import 'dart:io';
import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:get/get.dart' show Get, Inst;
import 'package:get_storage/get_storage.dart';
import 'package:logger/logger.dart';
import 'package:onechurch/core/app/services/api_urls.dart';
import 'package:onechurch/features/auth/controllers/auth_controller.dart'
    show AuthController;
import 'custom_logger.dart';
import 'dart:convert';

// ignore: constant_identifier_names
enum Method { POST, GET, PUT, DELETE, PATCH }

class HttpService {
  HttpService();
  static String? baseUrl = ApiUrls.baseUrl;
  static var box = GetStorage();
  Dio? dio = Dio(
    BaseOptions(
      baseUrl: baseUrl ?? "",
      receiveDataWhenStatusError: false,
      followRedirects: true,
      receiveTimeout: const Duration(minutes: 2),
      sendTimeout: const Duration(minutes: 1),
      validateStatus: (status) {
        return status! < 500;
      },
    ),
  );

  ///Always use this method to initialize the dio client
  Dio initializeDio() {
    dio?.interceptors.add(
      InterceptorsWrapper(
        onRequest: (
          RequestOptions options,
          RequestInterceptorHandler handler,
        ) async {
          options.headers = await header();

          // Handle logging for different data types
          String requestDataLog;
          if (options.data is FormData) {
            // For FormData, log the fields instead of trying to JSON encode
            final formData = options.data as FormData;
            final fields = formData.fields
                .map((field) => '${field.key}: ${field.value}')
                .join(', ');
            final files = formData.files
                .map(
                  (file) =>
                      '${file.key}: ${file.value.filename ?? 'unnamed file'}',
                )
                .join(', ');
            requestDataLog = "FormData - Fields: [$fields], Files: [$files]";
          } else {
            try {
              requestDataLog = jsonEncode(options.data);
            } catch (e) {
              requestDataLog =
                  "Unable to encode data: ${options.data.runtimeType}";
            }
          }

          logger.t(
            "REQUEST[${options.method}] => PATH: ${options.uri}\n"
            "REQUEST PARAMS: $requestDataLog\n"
            "HEADERS: ${options.headers}",
          );
          return handler.next(options);
        },
        onResponse: (response, handler) {
          logger.i(
            "RESPONSE[${response.statusCode}] => DATA: ${response.data}",
          );

          // Handle CORS proxy errors
          if (response.statusCode == 403 &&
              response.data.toString().contains('corsdemo')) {
            throw DioException(
              requestOptions: response.requestOptions,
              error:
                  "CORS proxy access not granted. Please visit https://cors-anywhere.herokuapp.com/corsdemo and click the button to temporarily unlock access.",
            );
          }

          return handler.next(response);
        },
        onError: (err, handler) {
          logger.e("DioError Type: ${err.type}");
          logger.e("DioError Message: ${err.message}");
          logger.e("DioError Response: ${err.response?.data}");
          logger.e("DioError Request: ${err.requestOptions.uri}");

          if (err.type == DioExceptionType.connectionTimeout ||
              err.type == DioExceptionType.sendTimeout ||
              err.type == DioExceptionType.receiveTimeout) {
            throw "Connection timeout - Please check your internet connection";
          } else if (err.type == DioExceptionType.connectionError) {
            throw "Connection error - Please check if the server is running and accessible";
          }

          return handler.next(err);
        },
      ),
    );
    return dio!;
  }

  // String? mAccessToken = box.read(CacheKeys.token);
  String? mAccessToken = "";

  DateTime? mAccessExpiresAt;
  var logger = Logger(filter: CustomLogFilter());

  header() async {
    mAccessToken = await FirebaseAuth.instance.currentUser?.getIdToken();
    return mAccessToken != null
        ? {
          "Content-Type": "application/json",
          "Authorization": "Bearer $mAccessToken",
        }
        : {"Content-Type": "application/json"};
  }

  Future<Response> request({
    required String url,
    required Method method,
    dynamic params,
    Options? options,
    FormData? formdata,
  }) async {
    Response response;
    try {
      String addOrgIdToUrl(String url) {
        bool isAuthEndpoint =
            url.contains('auth/') ||
            url == ApiUrls.register ||
            url == ApiUrls.login ||
            url == ApiUrls.confirmOtp ||
            url == ApiUrls.setPin ||
            url == ApiUrls.forgotPassword ||
            url == ApiUrls.forgotPasswordConfirm ||
            url == ApiUrls.resetPassword ||
            url == ApiUrls.verifyOtp ||
            url == ApiUrls.createHymn ||
            url == ApiUrls.groups ||
            url == ApiUrls.getMemberRelationshipType ||
            url == "member/category/";

        if (isAuthEndpoint) {
          return url;
        }

        // Get organization ID directly from AuthController
        final authController = Get.find<AuthController>();
        String orgId = authController.organizationId.value;

        if (orgId.isEmpty && authController.currentOrg.value?.id != null) {
          orgId = authController.currentOrg.value!.id!;
        }

        if (orgId.isNotEmpty) {
          if (url.contains("?")) {
            return "$url&organisation_id=$orgId";
          } else {
            return "$url?organisation_id=$orgId";
          }
        }

        return url;
      }

      final urlWithOrgId = addOrgIdToUrl(url);

      if (method == Method.POST) {
        if (formdata == null) {
          response = await dio!.post(
            urlWithOrgId,
            data: params,
            options: Options(
              validateStatus: (status) {
                return true;
              },
            ),
          );
        } else {
          response = await dio!.post(
            urlWithOrgId,
            data: formdata,
            options: options,
          );
        }
      } else if (method == Method.PUT) {
        response = await dio!.put(urlWithOrgId, data: params);
      } else if (method == Method.DELETE) {
        response = await dio!.delete(urlWithOrgId);
      } else if (method == Method.PATCH) {
        response = await dio!.patch(urlWithOrgId);
      } else {
        response = await dio!.get(urlWithOrgId);
      }
      return response;
    } on SocketException catch (e) {
      logger.e(e);
      throw Exception("Not Internet Connection");
    } on FormatException catch (e) {
      logger.e(e);

      throw Exception("Bad response format");
    } on DioException catch (e) {
      logger.e("DioError Type: ${e.type}");
      logger.e("DioError Message: ${e.message}");
      logger.e("DioError Response: ${e.response?.data}");
      logger.e("DioError Request: ${e.requestOptions.uri}");

      if (e.type == DioExceptionType.connectionTimeout ||
          e.type == DioExceptionType.sendTimeout ||
          e.type == DioExceptionType.receiveTimeout) {
        throw "Connection timeout - Please check your internet connection";
      } else if (e.type == DioExceptionType.connectionError) {
        throw "Connection error - Please check if the server is running and accessible";
      }

      rethrow;
    } catch (e) {
      logger.e(e);
      throw Exception("Something went wrong");
    }
  }
}
